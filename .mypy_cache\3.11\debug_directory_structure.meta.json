{"data_mtime": 1750778667, "dep_lines": [7, 8, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 30, 30, 30, 30], "dependencies": ["os", "pathlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "ae2e529b096766cf161dfa595f5301c0a07d9833", "id": "debug_directory_structure", "ignore_all": false, "interface_hash": "7b689c9028cbb6c03847ba69949a1e50aee7c1fd", "mtime": 1750778666, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag__code_server\\debug-testing\\debug_directory_structure.py", "plugin_data": null, "size": 6636, "suppressed": [], "version_id": "1.15.0"}