{"data_mtime": 1750874579, "dep_lines": [3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 601, 1136, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.middleware.cors", "fastapi.responses", "<PERSON><PERSON><PERSON>", "pydantic", "logging", "os", "sys", "pathlib", "chromadb", "ollama", "typing", "datetime", "code_preprocessor", "vector_db_creator", "traceback", "u<PERSON><PERSON>", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "asyncio", "asyncio.protocols", "chromadb.api", "chromadb.api.models", "chromadb.api.models.Collection", "chromadb.api.models.CollectionCommon", "chromadb.api.types", "chromadb.config", "configparser", "contextlib", "enum", "fastapi.applications", "fastapi.background", "fastapi.exceptions", "fastapi.openapi", "fastapi.openapi.models", "fastapi.param_functions", "fastapi.params", "fastapi.routing", "numpy", "numpy._typing", "ollama._client", "ollama._types", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.networks", "pydantic.types", "re", "starlette", "starlette.applications", "starlette.background", "starlette.exceptions", "starlette.middleware", "starlette.middleware.cors", "starlette.requests", "starlette.responses", "starlette.routing", "types", "typing_extensions", "uvicorn._types"], "hash": "7ef22fac71768f9f3cd2d55d7a589b5f828abebd", "id": "main", "ignore_all": false, "interface_hash": "b30cf39616c1f5140c521260d87f2b4c41bab8df", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag__code_server\\main.py", "plugin_data": null, "size": 48817, "suppressed": [], "version_id": "1.15.0"}