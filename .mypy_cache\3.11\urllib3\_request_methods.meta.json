{"data_mtime": 1750874736, "dep_lines": [5, 7, 8, 9, 10, 1, 3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["urllib.parse", "urllib3._base_connection", "urllib3._collections", "urllib3.filepost", "urllib3.response", "__future__", "json", "typing", "builtins", "_frozen_importlib", "_io", "abc", "io", "urllib3.fields"], "hash": "13b6ec42c7db497de5405f722b931ba95afa89e6", "id": "urllib3._request_methods", "ignore_all": true, "interface_hash": "906ed923e612b7706a216c4b289ff9524916088d", "mtime": 1750470649, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\_request_methods.py", "plugin_data": null, "size": 9931, "suppressed": [], "version_id": "1.15.0"}