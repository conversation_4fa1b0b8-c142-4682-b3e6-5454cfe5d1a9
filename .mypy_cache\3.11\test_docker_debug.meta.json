{"data_mtime": 1750780778, "dep_lines": [7, 8, 9, 64, 74, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["os", "sys", "time", "main", "importlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing"], "hash": "f19e6596595d8853551a0094146e78ad1606f1fb", "id": "test_docker_debug", "ignore_all": false, "interface_hash": "0381ec1a6250f2d781ffbd8d5bcb21a18a076dc7", "mtime": 1750780773, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag__code_server\\test_docker_debug.py", "plugin_data": null, "size": 3211, "suppressed": [], "version_id": "1.15.0"}